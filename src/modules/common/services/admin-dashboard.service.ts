import { sql } from "kysely";
import { db } from "@/database/connection";
import type { DashboardResponseDTO } from "../dtos";

// Helper untuk hitung trendType
const getTrendType = (value: number) =>
	value > 0 ? "increase" : value < 0 ? "decrease" : "neutral";

/**
 * 
 * Provide value like this for usersData:
 * [
  {
    "id": "total_employees",
    "title": "Total Karyawan",
    "total": 128,
    "trend": { "value": 0, "description": "Sama seperti kemarin", "trendType": "neutral" },
    "iconKey": "users_double"
  },
  {
    "id": "total_present",
    "title": "Total Karyawan Hadir",
    "total": 201,
    "trend": { "value": 10, "description": "Meningkat dibanding kemarin", "trendType": "increase" },
    "iconKey": "users_double"
  },
  {
    "id": "total_not_present",
    "title": "Total Karyawan Tidak Hadir",
    "total": 21,
    "trend": { "value": 10, "description": "Meningkat dibanding kemarin", "trendType": "increase" },
    "iconKey": "users_double"
  },
  {
    "id": "total_users_leave",
    "title": "Total Karyawan Cuti",
    "total": 3,
    "trend": { "value": 10, "description": "Meningkat dibanding kemarin", "trendType": "increase" },
    "iconKey": "users_double"
  }
]
 */

/**
 * Provide value like this for attendanceData:
 * 
const attendanceData = [
	{
		id: nanoid(),
		title: "Total Kariawan Datang Tepat Waktu",
		total: 125,
		trend: {
			value: 2,
			description: "Meningkat dibanding kemarin",
			color: "#00CE11",
			background: "#C9EACB",
		},
		icon: {
			component: IconsaxTimerStartIcon,
			color: "#00CE11",
			background: "#C9EACB",
		},
	},
	{
		id: nanoid(),
		title: "Total Kariawan Datang Terlambat",
		total: 3,
		trend: {
			value: 1,
			description: "Meningkat dibanding kemarin",
			color: "#F02D2D",
			background: "#FFB8B8",
		},
		icon: {
			component: IconsaxTimerStartIcon,
			color: "#F02D2D",
			background: "#FFB8B8",
		},
	},
	{
		id: nanoid(),
		title: "Total Kariawan Pulang Tepat Waktu",
		total: 125,
		trend: {
			value: 2,
			description: "Meningkat dibanding kemarin",
			color: "#00CE11",
			background: "#C9EACB",
		},
		icon: {
			component: IconsaxTimerStartIcon,
			color: "#00CE11",
			background: "#C9EACB",
		},
	},
	{
		id: nanoid(),
		title: "Total Kariawan Pulang Cepat",
		total: 3,
		trend: {
			value: 1,
			description: "Meningkat dibanding kemarin",
			color: "#F02D2D",
			background: "#FFB8B8",
		},
		icon: {
			component: IconsaxTimerStartIcon,
			color: "#F02D2D",
			background: "#FFB8B8",
		},
	},
];
 */
export const getDashboardData = async (): Promise<DashboardResponseDTO> => {
	const { total: totalEmployees } = await db
		.selectFrom("users")
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();
	const { total: totalEmployeesYesterday } = await db
		.selectFrom("users")
		.where("createdAt", "<", sql`CURRENT_DATE`)
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	return {
		usersData: [
			{
				id: "total_employees",
				title: "Total Karyawan",
				total: totalEmployees,
				trend: {
					value: totalEmployees - totalEmployeesYesterday,
					description:
						totalEmployees > totalEmployeesYesterday
							? "Meningkat dibanding kemarin"
							: totalEmployees < totalEmployeesYesterday
								? "Menurun dibanding kemarin"
								: "Sama seperti kemarin",
					trendType: getTrendType(totalEmployees - totalEmployeesYesterday),
				},
				iconKey: "users_double",
			},
		],
	};
};
