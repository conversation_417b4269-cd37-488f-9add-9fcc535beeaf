import z from "zod";
import { leaveRequestResponseDTO } from "@/modules/leave/dtos/response";
import { officeLeaveResponseDTO } from "@/modules/office-leave/dtos/response";

export const dashboardResponseDTO = z.object({
	usersData: z.array(
		z.object({
			id: z.string(),
			title: z.string(),
			total: z.number(),
			trend: z.object({
				value: z.number(),
				description: z.string(),
				trendType: z.enum(["neutral", "increase", "decrease"]),
			}),
			iconKey: z.string(),
		}),
	),
	attendanceData: z.array(
		z.object({
			id: z.string(),
			title: z.string(),
			total: z.number(),
			trend: z.object({
				value: z.number(),
				description: z.string(),
				trendType: z.enum(["neutral", "increase", "decrease"]),
			}),
			iconKey: z.string(),
		}),
	),
	latestLeaveRequests: z.array(leaveRequestResponseDTO),
	latestOfficeLeaves: z.array(officeLeaveResponseDTO),
});
export type DashboardResponseDTO = z.infer<typeof dashboardResponseDTO>;
